<script lang="ts" module>
	import { flatten, unFlatten, type DocumentNode } from '$lib/utils'

	function isVisible<DTO extends DocumentNode>(
		data?: Partial<DTO>,
		hide?: string | ((data: Partial<DTO>) => boolean) | boolean,
	) {
		if (!data) return false

		switch (typeof hide) {
			case 'undefined':
				return true
			case 'boolean':
				return !hide
			case 'string':
				// eslint-disable-next-line no-case-declarations
				const expression = Function('data', `return ${hide};`)
				return !expression(data)
			case 'function':
				return !hide(data)
			default:
				return true
		}
	}

	function isDisabled<DTO extends DocumentNode>(data?: Partial<DTO>, disabled?: boolean, dependent?: string[]) {
		if (disabled === true) return true
		if (!data) return true

		if (!dependent?.length) return false

		return dependent.some((key) => !data[key]?.length)
	}
</script>

<script lang="ts" generics="DTO extends DocumentNode">
	import { ProgressRing, Tabs } from '@skeletonlabs/skeleton-svelte'
	import { onMount, tick } from 'svelte'

	import { goto } from '$app/navigation'
	import { page } from '$app/state'

	import {
		type Form,
		FieldType,
		type FieldGroup,
		type FieldTabs,
		type Field,
		type BaseProperties,
	} from './form.interface'
	import FormField from './FormField.svelte'

	import type { NestedFields } from '$lib/utils'

	let {
		fields = $bindable(),
		value = $bindable({} as Partial<DTO>),
		searchParam = undefined,
		variant = 'primary',
		isLoading = $bindable(false),

		formClass = 'mx-auto w-full space-y-8',

		titleSize = 'h4',
		titleClass = 'text-surface-900 dark:text-surface-100',

		groupClass = 'card bg-noise bg-surface-50-950 space-y-8 not-first:mt-8 border border-surface-100 dark:border-surface-800 p-8',
		tabsClass = 'card bg-noise bg-surface-50-950 space-y-8 not-first:mt-8',

		groupHeaderClass = 'mb-6',
		tagsHeaderClass = 'mb-6',

		descriptionClass = 'mt-2',
		descriptionFont = 'text-s font-light whitespace-pre-line text-surface-700 dark:text-surface-300',

		fieldLabelClass = 'mb-2 text-surface-800 dark:text-surface-200',
		fieldDescriptionClass = 'mt-3 text-surface-700 dark:text-surface-300',

		footerClass = 'flex justify-end space-x-4 pb-8',

		onValueChange,

		submit = undefined,
		onSubmit,

		close = false,
		onClose,

		reset = false,
		onReset,

		onDirtyChange,
	}: {
		fields: Form<DTO>
		value?: Partial<DTO>
		searchParam?: string | undefined
		variant?: 'primary' | 'secondary' | 'tertiary' | 'outline'
		isLoading?: boolean

		formClass?: string

		titleSize?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
		titleClass?: string

		groupClass?: string
		tabsClass?: string

		groupHeaderClass?: string
		tagsHeaderClass?: string

		descriptionClass?: string
		descriptionFont?: string

		fieldLabelClass?: string
		fieldDescriptionClass?: string

		footerClass?: string

		onValueChange?: (data: Partial<DTO>) => Promise<void> | void

		submit?: string
		onSubmit?: (data: DTO) => Promise<void> | void

		close?: boolean | string
		onClose?: (data: Partial<DTO>) => Promise<void> | void

		reset?: boolean | string
		onReset?: (data: Partial<DTO>) => Promise<void> | void

		onDirtyChange?: (isDirty: boolean) => void
	} = $props()

	const data = $state(flatten(value))
	const tabGroup = $state<{ [key: number]: string }>({})

	let initialData = $state('{}')

	const isDirty = $derived(JSON.stringify(value) !== initialData)
	$effect(() => {
		$inspect(fields)
		onDirtyChange?.(isDirty)
	})

	const fattenFields = fields.flatMap(
		(field) =>
			(field as FieldGroup<DTO>).fields ??
			(field as FieldTabs<DTO>).tabs?.flatMap((field) => (field as FieldGroup<DTO>).fields ?? field) ??
			field,
	)

	for (const field of fattenFields) {
		if (field.type === FieldType.BREAK) continue
		if (data[field.key] !== undefined) continue

		if (field.props.default !== undefined) {
			data[field.key] = field.props.default as DTO[typeof field.key]
		}
	}

	function updateSearchParams() {
		if (searchParam) {
			const currentFormData = $state.snapshot(data) as DTO
			for (const key in currentFormData) {
				if (currentFormData[key] === undefined || currentFormData[key] === null || !currentFormData[key].length) {
					delete currentFormData[key]
				}
			}

			const newUrl = new URL(page.url)
			newUrl.searchParams.set(searchParam, btoa(JSON.stringify(currentFormData)))
			goto(newUrl, { replaceState: true, noScroll: true, keepFocus: true })
		}
	}

	async function onChange<T extends NestedFields<DTO>>(key: T | 'break') {
		if (key === 'break') return

		for (const field of fattenFields) {
			if (field.key !== 'break') {
				if (field.resetOn?.length && field.resetOn.includes(key)) {
					if (data[field.key] !== (field.props as BaseProperties<DTO, T>).default && data[field.key] !== null) {
						;(data[field.key] as unknown) = (field.props as BaseProperties<DTO, T>).default ?? null
					}
				}
			}
		}

		await tick()

		value = unFlatten(data)
		onValueChange?.(value)
		updateSearchParams()
	}

	function resetForm() {
		const snapshot = JSON.parse(initialData)
		for (const key in snapshot) {
			if (key !== 'break') {
				;(data[key] as unknown) = snapshot[key]
			}
		}

		value = unFlatten(data)
		onReset?.(value)
		updateSearchParams()
	}

	onMount(async () => {
		if (searchParam && page.url.searchParams.has(searchParam)) {
			const defaultValues = JSON.parse(
				page.url.searchParams.get(searchParam) ? atob(page.url.searchParams.get(searchParam) as string) : '{}',
			)

			for (const key in defaultValues) {
				if (key !== 'break') {
					;(data[key] as unknown) = defaultValues[key]
				}
			}
		}

		await tick()

		value = unFlatten(data)
		onValueChange?.(value)
		updateSearchParams()

		initialData = JSON.stringify(structuredClone($state.snapshot(data)))
	})

	function isGroup(field: unknown): field is FieldGroup<DTO> {
		return (field as FieldGroup<DTO>).fields !== undefined
	}
	function isTabs(field: unknown): field is FieldTabs<DTO> {
		return (field as FieldTabs<DTO>).tabs !== undefined
	}
	function isField(field: unknown): field is Field<DTO> {
		return (field as FieldGroup<DTO>).fields === undefined && (field as FieldTabs<DTO>).tabs === undefined
	}
</script>

<form
	onsubmit={(e) => {
		e.preventDefault()
		value = unFlatten(data)
		initialData = JSON.stringify(structuredClone(value))
		onSubmit?.(value as DTO)
	}}
	class="form-builder {formClass}"
>
	{#each fields as field, index (field)}
		{#if isGroup(field)}
			{#if isVisible(value, field.hide)}
				<div class={groupClass}>
					<div class={groupHeaderClass}>
						<svelte:element this={titleSize} class="{titleSize} {titleClass}">{field.title}</svelte:element>

						{#if field.divider}
							<hr class="hr" />
						{/if}

						{#if field.description}
							<p class="{descriptionFont} {descriptionClass}">{field.description}</p>
						{/if}
					</div>

					{#each field.fields as nestedField (nestedField.key)}
						{#if isVisible(value, nestedField.hide)}
							{nestedField.key}
							<FormField
								field={nestedField}
								bind:value={data[nestedField.key]}
								onValueChange={() => onChange(nestedField.key)}
								disabled={isDisabled(data, nestedField.props?.disabled, nestedField.dependent)}
								readOnly={isDisabled(data, nestedField.props?.readOnly, nestedField.dependent)}
								labelClass={fieldLabelClass}
								descriptionClass={fieldDescriptionClass}
							/>
						{/if}
					{/each}
				</div>
			{/if}
		{:else if isTabs(field)}
			{#if isVisible(value, field.hide)}
				<div class={tabsClass}>
					<div class={tagsHeaderClass}>
						{#if field.title}
							<svelte:element this={titleSize} class="{titleSize} {titleClass}">{field.title}</svelte:element>
						{/if}

						{#if field.divider}
							<hr class="hr" />
						{/if}

						{#if field.description}
							<p class="{descriptionFont} {descriptionClass}">{field.description}</p>
						{/if}
					</div>

					<Tabs value={tabGroup[index]} onValueChange={(e) => (tabGroup[index] = e.value)} fluid>
						{#snippet list()}
							{#each field.tabs as { title } (title)}
								<Tabs.Control value={title}>{title}</Tabs.Control>
							{/each}
						{/snippet}

						{#snippet content()}
							<div class="mx-auto w-full space-y-4">
								{#each field.tabs as { title, fields } (title)}
									<Tabs.Panel value={title}>
										{#each fields as nestedField (nestedField.key)}
											{#if isVisible(value, nestedField.hide)}
												{nestedField.key}
												<FormField
													field={nestedField}
													value={data[nestedField.key]}
													onValueChange={() => onChange(nestedField.key)}
													disabled={isDisabled(data, nestedField.props?.disabled, nestedField.dependent)}
													readOnly={isDisabled(data, nestedField.props?.readOnly, nestedField.dependent)}
													labelClass={fieldLabelClass}
													descriptionClass={fieldDescriptionClass}
												/>
											{/if}
										{/each}
									</Tabs.Panel>
								{/each}
							</div>
						{/snippet}
					</Tabs>
				</div>
			{/if}
		{:else if isField(field)}
			{#if isVisible(value, field.hide)}
				{field.key}
				<FormField
					{field}
					bind:value={data[field.key]}
					onValueChange={() => isField(field) && onChange(field.key)}
					disabled={isDisabled(data, field.props?.disabled, field.dependent)}
					readOnly={isDisabled(data, field.props?.readOnly, field.dependent)}
					labelClass={fieldLabelClass}
					descriptionClass={fieldDescriptionClass}
				/>
			{/if}
		{/if}
	{/each}

	{#if submit || close || reset}
		<footer class={footerClass}>
			{#if close}
				<button
					type="button"
					class="btn preset-outlined-tertiary-500"
					onclick={() => onClose?.($state.snapshot(data) as DTO)}>{typeof close === 'string' ? close : 'Close'}</button
				>
			{/if}

			{#if reset && isDirty}
				<button type="button" class="btn preset-outlined-tertiary-500" onclick={resetForm}
					>{typeof reset === 'string' ? reset : 'Reset'}</button
				>
			{/if}

			{#if submit}
				{#if isLoading}
					<ProgressRing
						size="size-9"
						strokeWidth="100px"
						meterStroke="stroke-secondary-500"
						trackStroke="stroke-secondary-500/30"
					/>
				{:else}
					<button type="submit" class="btn preset-filled-{variant}-500" disabled={!isDirty}>{submit}</button>
				{/if}
			{/if}
		</footer>
	{/if}
</form>
